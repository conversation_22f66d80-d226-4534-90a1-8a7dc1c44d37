'use client';

import i18n from '@/providers/i18n/i18n';
import ROUTES from '@/router/routes';
import type { Meta, StoryObj } from '@storybook/nextjs-vite';
import { expect, userEvent, waitFor, within } from 'storybook/test';
import Page from './page';

const meta = {
  title: 'Pages/Auth/LoginPage',
  component: Page,
  parameters: {
    layout: 'fullscreen',
  },
  decorators: [
    (Story, context) => {
      const locale = context.globals.locale || 'en';
      i18n.changeLanguage(locale);
      return (
        <div style={{ width: '1200px', margin: '0 auto' }}>
          <Story />
        </div>
      );
    },
  ],
} satisfies Meta<typeof Page>;

export default meta;

type Story = StoryObj<typeof meta>;

export const Default: Story = {
  play: async ({ canvasElement, step }) => {
    const canvas = within(canvasElement);

    await step('Should render form elements', async () => {
      expect(await canvas.findByTestId('accessId')).toBeInTheDocument();
      expect(await canvas.findByTestId('accessIdOAuth')).toBeInTheDocument();
      expect(await canvas.findByTestId('username')).toBeInTheDocument();
      expect(await canvas.findByTestId('password')).toBeInTheDocument();
      expect(await canvas.findByTestId('forgotPassword')).toHaveAttribute(
        'href',
        ROUTES.AUTH['RESET_PASSWORD']
      );
      expect(await canvas.findByTestId('submit')).toBeInTheDocument();
    });

    await step('Should show error alert for invalid credentials', async () => {
      const usernameInput = await canvas.findByTestId('username');
      const passwordInput = await canvas.findByTestId('password');
      await userEvent.clear(usernameInput);
      await userEvent.clear(passwordInput);

      await userEvent.type(usernameInput, 'wronguser');
      await userEvent.type(passwordInput, 'wrongp@ssword');
      await userEvent.click(await canvas.findByTestId('submit'));

      await waitFor(() => {
        expect(canvas.getByText(/the username or password is incorrect/i)).toBeInTheDocument();
      });
    });

    await step('Should reject non-numeric input in access ID', async () => {
      const accessIdInput = await canvas.findByTestId('accessId');
      await userEvent.type(accessIdInput, 'testaccessid');
      await waitFor(() => {
        expect(accessIdInput).toHaveValue('');
      });
    });

    await step('Should toggle password visibility', async () => {
      const passwordInput = await canvas.findByTestId('password');
      await userEvent.clear(passwordInput);
      await userEvent.type(passwordInput, 'wrongp@ssword');

      expect(passwordInput).toHaveAttribute('type', 'password');

      const toggleButton = await canvas.findByTestId('passwordToggle');
      await userEvent.click(toggleButton);

      expect(passwordInput).toHaveAttribute('type', 'text');
    });

    await step('Should support keyboard tabbing', async () => {
      const inputs = {
        accessId: await canvas.findByTestId('accessId'),
        username: await canvas.findByTestId('username'),
        password: await canvas.findByTestId('password'),
        toggle: await canvas.findByTestId('passwordToggle'),
        forgot: await canvas.findByTestId('forgotPassword'),
        submit: await canvas.findByTestId('submit'),
      };

      inputs.accessId.focus();
      expect(document.activeElement).toBe(inputs.accessId);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.username);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.password);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.toggle);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.forgot);

      await userEvent.tab();
      expect(document.activeElement).toBe(inputs.submit);
    });
  },
};
