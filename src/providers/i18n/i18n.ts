import i18n from 'i18next';
import LanguageDetector from 'i18next-browser-languagedetector';
import HttpBackend from 'i18next-http-backend';
import { initReactI18next } from 'react-i18next';

const backendUrl = process.env.NEXT_PUBLIC_S3_TRANSLATIONS_URL;

i18n
  .use(HttpBackend)
  .use(LanguageDetector) // detect language from browser/localStorage
  .use(initReactI18next)
  .init({
    supportedLngs: ['en', 'zh'], // Explicitly list supported languages
    fallbackLng: 'en', // Fallback if not detected or unsupported
    defaultNS: 'bo', // Default and additional namespaces
    ns: ['bo'],
    interpolation: {
      escapeValue: false,
    },
    nonExplicitSupportedLngs: true,
    backend: {
      loadPath: backendUrl,
      reloadInterval: 60000,
    },
    debug: process.env.NODE_ENV === 'development',
    preload: ['en', 'zh'],
  })

  .catch((error) => {
    // eslint-disable-next-line no-console
    console.error('i18n initialization error:', error);
  });

export default i18n;
