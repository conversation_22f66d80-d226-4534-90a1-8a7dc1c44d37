import { FormAlert } from '@/components/base/feedback/alert';
import { RouterLink } from '@/components/base/navigation/router-link';
import { Typography } from '@/components/base/typography';
import { getAlertFromErrorCode } from '@/constants/alert.constant';
import { useAuth } from '@/hooks/auth/use-auth.hook';
import { useRouter } from '@/hooks/navigation/use-router.hook';
import ROUTES from '@/router/routes';
import {
  defaultNormalLoginValues,
  defaultOAuthLoginValues,
  normalLoginSchema,
  oAuthLoginSchema,
  type NormalLoginFormValues,
  type OAuthLoginFormValues,
} from '@/schemas/auth';
import { authService } from '@/services/auth/auth.service';
import { isSuccessResponse } from '@/types/api-responses.type';
import { zodResolver } from '@hookform/resolvers/zod';
import Box from '@mui/material/Box';
import Container from '@mui/material/Container';
import Divider from '@mui/material/Divider';
import Grid from '@mui/material/Grid';
import Link from '@mui/material/Link';
import { useTheme } from '@mui/material/styles';
import useMediaQuery from '@mui/material/useMediaQuery';
import { useCallback, useState, type JSX } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import {
  AccessIdField,
  DemoCredentials,
  FormContainer,
  FormHeader,
  OAuthButton,
  PasswordField,
  SubmitButton,
  UsernameField,
} from '../AuthFormComponents';
import { authAlertResolver } from './LoginForm.constant';
import { oAuthProviders, type AlertState } from './LoginForm.type';

/**
 * LoginForm is a component that renders a form for users to sign in.
 *
 * It includes:
 * - OAuth sign-in options (Google)
 * - Username and password fields
 * - "Forgot password" link
 * - Form validation
 *
 * @example
 * ```tsx
 * <LoginForm />
 * ```
 */
export function LoginForm(): JSX.Element {
  const router = useRouter();
  const { checkSession } = useAuth();
  const [isPending, setIsPending] = useState<boolean>(false);
  const [alert, setAlert] = useState<AlertState>(null);

  const { t } = useTranslation();

  const {
    register,
    handleSubmit,
    setError,
    formState: { errors },
  } = useForm<NormalLoginFormValues>({
    defaultValues: defaultNormalLoginValues,
    resolver: zodResolver(normalLoginSchema(t)),
  });

  const {
    register: registerOAuth,
    handleSubmit: handleSubmitOAuth,
    formState: { errors: errorsOAuth },
  } = useForm<OAuthLoginFormValues>({
    defaultValues: defaultOAuthLoginValues,
    resolver: zodResolver(oAuthLoginSchema(t)),
  });

  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const dividerOrientation = isMobile ? 'horizontal' : 'vertical';

  const onSubmit = useCallback(
    async (values: NormalLoginFormValues): Promise<void> => {
      setIsPending(true);
      setAlert(null);

      const response = await authService.signInWithPassword(values);

      // Handle error response
      if (!isSuccessResponse(response)) {
        const alertData = getAlertFromErrorCode(
          response.errorCode,
          response.message,
          authAlertResolver
        );

        setAlert(alertData);

        setError('root', {
          type: 'server',
          message: response.message,
        });

        // Scroll to top to make sure alert is visible
        window.scrollTo({ top: 0, behavior: 'smooth' });

        setIsPending(false);
        return;
      }

      // Handle success response
      setAlert({
        severity: 'success',
        title: 'login successful',
        message: response.message,
      });

      // Scroll to top to make sure alert is visible
      window.scrollTo({ top: 0, behavior: 'smooth' });

      setIsPending(false);
      await checkSession();

      router.refresh();
    },
    [router, setError, checkSession]
  );

  return (
    <>
      {/* Alert bars */}
      <Container maxWidth="lg">
        <Grid
          container
          spacing={1}
          py={3}
        >
          {alert && (
            <Grid size={12}>
              <FormAlert
                severity={alert.severity}
                title={alert.title}
                message={alert.message}
              />
            </Grid>
          )}

          {/* Demo Credentials */}
          <DemoCredentials />
        </Grid>
      </Container>

      <FormHeader
        title={t('common.label.signIn')}
        subtitle={t('common.sentence.accessAccountContinueJourney')}
      />
      <FormContainer>
        <Box
          display="flex"
          flexDirection={{ xs: 'column', md: 'row' }}
          alignItems="center"
          justifyContent="center"
          sx={{
            gap: {
              sm: 8,
              md: 0,
              lg: 8,
            },
          }}
        >
          {/* Normal Login Form */}
          <form onSubmit={handleSubmit(onSubmit)}>
            <Container
              maxWidth="sm"
              sx={{ px: 2, py: 3 }}
            >
              <Grid
                container
                spacing={1}
              >
                {/* Access ID Field */}
                <AccessIdField
                  register={register}
                  errors={errors}
                  isPending={isPending}
                  dataTestId="accessId"
                />

                {/* Username Field */}
                <UsernameField
                  register={register}
                  errors={errors}
                  isPending={isPending}
                />

                {/* Password Field */}
                <PasswordField
                  register={register}
                  errors={errors}
                  isPending={isPending}
                />

                {/* Forgot Password */}
                <Link
                  component={RouterLink}
                  href={ROUTES.AUTH['RESET_PASSWORD']}
                  underline="hover"
                  data-testid="forgotPassword"
                >
                  <Typography caseTransform="sentenceCase">
                    {t('common.label.forgotPassword')}
                  </Typography>
                </Link>

                {/* Submit Button */}
                <SubmitButton
                  label={t('common.label.signIn')}
                  isPending={isPending}
                />
              </Grid>
            </Container>
          </form>

          {/* Divider */}
          <Divider
            orientation={dividerOrientation}
            flexItem
            aria-orientation={dividerOrientation}
          >
            <Typography caseTransform="uppercase">{t('common.label.or')}</Typography>
          </Divider>

          {/* OAuth Login Form */}
          <form onSubmit={handleSubmitOAuth(() => {})}>
            {/* OAuth Providers */}
            <Container
              maxWidth="sm"
              sx={{ px: 2, py: 3 }}
            >
              <Grid
                container
                spacing={1}
              >
                {/* Access ID Field */}
                <AccessIdField
                  register={registerOAuth}
                  errors={errorsOAuth}
                  isPending={isPending}
                  dataTestId={'accessIdOAuth'}
                />

                {/* OAuth Button */}
                {oAuthProviders.map((provider) => (
                  <OAuthButton
                    key={provider.id}
                    label={t('common.label.signInWithProvider', { provider: provider.name })}
                    logo={provider.logo}
                    altText={provider.name}
                    isPending={isPending}
                  ></OAuthButton>
                ))}
              </Grid>
            </Container>
          </form>
        </Box>
      </FormContainer>
    </>
  );
}

export default LoginForm;
