import type { AlertData } from '@/constants/alert.constant';

/**
 * Resolves authentication-related error codes to AlertData objects for displaying error messages to the user.
 *
 * @param errorCode - The error code received from the authentication service.
 * @param message - An optional custom error message to display to the user.
 *
 * @returns An AlertData object containing the severity, title, and message to be displayed to the user.
 *          If the errorCode is not recognized, a default error AlertData object is returned.
 *          If the message parameter is provided, it will be used as the fallback message.
 *          Otherwise, a default fallback message will be used.
 */
export const authAlertResolver = (errorCode?: string, message?: string): AlertData | null => {
  const fallbackMessage =
    message ?? 'Something went wrong while trying to sign you in. Please try again later.';

  switch (errorCode) {
    case '20001':
      return {
        severity: 'error',
        title: 'Invalid username or password',
        message: fallbackMessage,
      };
    case '20002':
      return {
        severity: 'error',
        title: 'Session expired',
        message: fallbackMessage,
      };
    case '20003':
      return {
        severity: 'error',
        title: 'Account inactive',
        message: fallbackMessage,
      };
    case '20004':
      return {
        severity: 'error',
        title: 'Suspicious activity detected',
        message: fallbackMessage,
      };
    case '20005':
      return {
        severity: 'error',
        title: 'Account locked due to too many attempts',
        message: fallbackMessage,
      };
    case '20006':
      return {
        severity: 'error',
        title: 'Account locked',
        message: fallbackMessage,
      };
    case '20007':
      return {
        severity: 'info',
        title: 'Two factor authentication setup required',
        message:
          'To enhance security, you must set up Two-Factor Authentication (2FA) before accessing your account. Please follow the setup instructions in your account settings',
      };
    case '20008':
      return {
        severity: 'info',
        title: 'Two factor authentication required',
        message:
          'Your account requires 2FA verification. Please enter the authentication code from your authenticator app',
      };
    default:
      return {
        severity: 'error',
        title: 'Failed',
        message: fallbackMessage,
      };
  }
};
